package dev.pigmomo.yhkit2025.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dev.pigmomo.yhkit2025.R
import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitoringPlanEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductMonitorEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductChangeRecordEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductChangeType
import dev.pigmomo.yhkit2025.ui.theme.cardThemeOverlay
import dev.pigmomo.yhkit2025.ui.components.TokenActionButton
import dev.pigmomo.yhkit2025.viewmodel.MonitoringDataViewModel
import java.text.SimpleDateFormat
import java.util.*

/**
 * 监控数据展示Screen
 * 用于展示所有监控商品的详细数据和变化记录
 * @param viewModel 监控数据视图模型
 * @param onNavigateBack 返回导航回调
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MonitoringDataScreen(
    viewModel: MonitoringDataViewModel,
    onNavigateBack: () -> Unit
) {
    // 从ViewModel获取状态
    val monitoredProducts by viewModel.monitoredProducts.collectAsState()
    val selectedProduct by viewModel.selectedProduct.collectAsState()
    val selectedProductChangeRecords by viewModel.selectedProductChangeRecords.collectAsState()
    val isLoading by viewModel.isLoading
    val errorMessage by viewModel.errorMessage

    // 从ViewModel获取格式化器
    val dateFormat = viewModel.dateFormat
    val fullDateFormat = viewModel.fullDateFormat

    // 显示错误信息
    errorMessage?.let { message ->
        LaunchedEffect(message) {
            // 可以在这里显示Toast或Snackbar
            viewModel.clearErrorMessage()
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("监控数据") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                },
                actions = {
                    IconButton(onClick = { viewModel.refreshData() }) {
                        Icon(Icons.Default.Refresh, contentDescription = "刷新")
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primary,
                    titleContentColor = Color.White,
                    navigationIconContentColor = Color.White,
                    actionIconContentColor = Color.White
                )
            )
        }
    ) { paddingValues ->
        if (isLoading) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else {
            if (selectedProduct == null) {
                // 显示商品列表
                ProductListScreen(
                    products = monitoredProducts,
                    onProductClick = { product ->
                        viewModel.selectProduct(product)
                    },
                    modifier = Modifier.padding(paddingValues)
                )
            } else {
                // 显示商品详情和变化记录
                ProductDetailScreen(
                    product = selectedProduct!!,
                    changeRecords = selectedProductChangeRecords,
                    dateFormat = dateFormat,
                    fullDateFormat = fullDateFormat,
                    onBackClick = { viewModel.selectProduct(null) },
                    modifier = Modifier.padding(paddingValues)
                )
            }
        }
    }
}

/**
 * 商品列表页面
 */
@Composable
fun ProductListScreen(
    products: List<ProductMonitorEntity>,
    onProductClick: (ProductMonitorEntity) -> Unit,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        item {
            Text(
                text = "监控商品列表 (${products.size})",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 8.dp)
            )
        }

        items(products) { product ->
            ProductListItem(
                product = product,
                onClick = {
                    android.util.Log.d("ProductListScreen", "Clicked product: ${product.id}")
                    onProductClick(product)
                }
            )
        }
    }
}

/**
 * 商品列表项组件
 */
@Composable
fun ProductListItem(
    product: ProductMonitorEntity,
    onClick: () -> Unit
) {
    val dateFormat = remember { SimpleDateFormat("MM-dd HH:mm", Locale.getDefault()) }

    Card(
        colors = cardThemeOverlay(),
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            // 商品标题
            Text(
                text = product.title,
                fontWeight = FontWeight.Medium,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis
            )

            if (product.subtitle.isNotEmpty()) {
                Text(
                    text = product.subtitle,
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            // 商品信息行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 价格信息
                if (product.currentPrice > 0) {
                    Text(
                        text = "¥${String.format("%.2f", product.currentPrice / 100.0)}",
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary
                    )
                }

                // 状态标签
                Surface(
                    color = when {
                        product.available == 1 -> Color(0xFF4CAF50)
                        product.isSeckill == 1 -> Color(0xFFFF9800)
                        else -> Color(0xFFF44336)
                    },
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Text(
                        text = when {
                            product.available == 1 -> "有货"
                            product.isSeckill == 1 -> "秒杀"
                            else -> "缺货"
                        },
                        color = Color.White,
                        fontSize = 10.sp,
                        modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
                    )
                }
            }

            Spacer(modifier = Modifier.height(4.dp))

            // 更新时间
            Text(
                text = "更新时间: ${dateFormat.format(product.lastUpdateTime)}",
                fontSize = 11.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

/**
 * 商品详情页面
 */
@Composable
fun ProductDetailScreen(
    product: ProductMonitorEntity,
    changeRecords: List<ProductChangeRecordEntity>,
    dateFormat: SimpleDateFormat,
    fullDateFormat: SimpleDateFormat,
    onBackClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxSize()
    ) {
        // 返回按钮和标题
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onBackClick) {
                Icon(
                    imageVector = Icons.Default.ArrowBack,
                    contentDescription = "返回"
                )
            }
            Text(
                text = "商品详情",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(start = 8.dp)
            )
        }

        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // 商品信息卡片
            item {
                ProductDetailCard(product, dateFormat)
            }

            // 变化记录标题
            item {
                Text(
                    text = "变化记录 (${changeRecords.size})",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
                // 调试信息
                android.util.Log.d("ProductDetailScreen", "Displaying ${changeRecords.size} change records for product ${product.id}")
            }

            // 变化记录列表
            if (changeRecords.isEmpty()) {
                item {
                    Card(
                        colors = cardThemeOverlay(),
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(32.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = "暂无变化记录",
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
            } else {
                items(changeRecords) { record ->
                    ChangeRecordItem(record, fullDateFormat)
                }
            }
        }
    }
}

/**
 * 商品详情卡片
 */
@Composable
fun ProductDetailCard(
    product: ProductMonitorEntity,
    dateFormat: SimpleDateFormat
) {
    Card(
        colors = cardThemeOverlay(),
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 商品标题
            Text(
                text = product.title,
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold
            )

            if (product.subtitle.isNotEmpty()) {
                Text(
                    text = product.subtitle,
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.padding(top = 4.dp)
                )
            }

            Spacer(modifier = Modifier.height(12.dp))

            // 商品基本信息
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Column {
                    Text(
                        text = "商品ID",
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = product.id,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium
                    )
                }

                if (product.originalSkuCode.isNotEmpty()) {
                    Column(
                        horizontalAlignment = Alignment.End
                    ) {
                        Text(
                            text = "SKU代码",
                            fontSize = 12.sp,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Text(
                            text = product.originalSkuCode,
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            // 价格和状态信息
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = "当前价格",
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    if (product.currentPrice > 0) {
                        Text(
                            text = "¥${String.format("%.2f", product.currentPrice / 100.0)}",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.primary
                        )
                    } else {
                        Text(
                            text = "暂无价格",
                            fontSize = 14.sp,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }

                // 状态标签
                Surface(
                    color = when {
                        product.available == 1 -> Color(0xFF4CAF50)
                        product.isSeckill == 1 -> Color(0xFFFF9800)
                        else -> Color(0xFFF44336)
                    },
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Text(
                        text = when {
                            product.available == 1 -> "有货"
                            product.isSeckill == 1 -> "秒杀"
                            else -> "缺货"
                        },
                        color = Color.White,
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium,
                        modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp)
                    )
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            // 时间信息
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Column {
                    Text(
                        text = "首次添加",
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = dateFormat.format(product.firstAddTime),
                        fontSize = 12.sp
                    )
                }

                Column(
                    horizontalAlignment = Alignment.End
                ) {
                    Text(
                        text = "最后更新",
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = dateFormat.format(product.lastUpdateTime),
                        fontSize = 12.sp
                    )
                }
            }

            // 监控状态
            if (product.isMonitoringEnabled) {
                Spacer(modifier = Modifier.height(8.dp))
                Surface(
                    color = Color(0xFF4CAF50).copy(alpha = 0.1f),
                    shape = RoundedCornerShape(8.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(
                        text = "✓ 监控已启用",
                        color = Color(0xFF4CAF50),
                        fontSize = 12.sp,
                        modifier = Modifier.padding(8.dp)
                    )
                }
            }
        }
    }
}

/**
 * 变化记录项
 */
@Composable
fun ChangeRecordItem(
    record: ProductChangeRecordEntity,
    dateFormat: SimpleDateFormat
) {
    Card(
        colors = cardThemeOverlay(),
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = record.productId,
                    fontWeight = FontWeight.Medium,
                    fontSize = 12.sp,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.weight(1f)
                )

                Surface(
                    color = when (record.changeType) {
                        ProductChangeType.PRICE_CHANGE -> Color(0xFF2196F3)
                        ProductChangeType.STOCK_CHANGE -> Color(0xFF4CAF50)
                        ProductChangeType.AVAILABILITY_CHANGE -> Color(0xFFFF9800)
                        ProductChangeType.INFO_CHANGE -> Color(0xFF9C27B0)
                        ProductChangeType.SECKILL_STATUS_CHANGE -> Color(0xFFF44336)
                        else -> Color(0xFF607D8B)
                    },
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Text(
                        text = when (record.changeType) {
                            ProductChangeType.PRICE_CHANGE -> "价格"
                            ProductChangeType.STOCK_CHANGE -> "库存"
                            ProductChangeType.AVAILABILITY_CHANGE -> "可用性"
                            ProductChangeType.INFO_CHANGE -> "信息"
                            ProductChangeType.SECKILL_STATUS_CHANGE -> "秒杀"
                            else -> "其他"
                        },
                        color = Color.White,
                        fontSize = 10.sp,
                        modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
                    )
                }
            }

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = "字段: ${record.fieldName}",
                fontSize = 12.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            if (record.oldValue.isNotEmpty() || record.newValue.isNotEmpty()) {
                Text(
                    text = "${record.oldValue} → ${record.newValue}",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.primary
                )
            }

            Text(
                text = dateFormat.format(record.changeTime),
                fontSize = 11.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}


